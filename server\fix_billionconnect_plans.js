const { EsimPlan, Provider } = require('./src/models');
const billionconnectService = require('./src/services/billionconnect.service');
const providerFactory = require('./src/services/provider.factory');

async function fixBillionConnectPlans() {
    console.log('🔧 Fixing BillionConnect Plans - Capacity-Based Logic\n');
    
    try {
        // 1. Find BillionConnect provider
        const provider = await Provider.findOne({
            where: { name: 'billionconnect' }
        });
        
        if (!provider) {
            console.log('❌ BillionConnect provider not found in database');
            return;
        }
        
        console.log(`✅ Found BillionConnect provider: ${provider.name} (ID: ${provider.id})`);
        
        // 2. Find all BillionConnect plans
        const billionConnectPlans = await EsimPlan.findAll({
            where: { providerId: provider.id },
            attributes: ['id', 'name', 'externalSkuId', 'planType', 'planData', 'planDataUnit', 'providerMetadata']
        });
        
        console.log(`\n📊 Found ${billionConnectPlans.length} BillionConnect plans in database`);
        
        if (billionConnectPlans.length === 0) {
            console.log('ℹ️  No BillionConnect plans found. Run sync first.');
            return;
        }
        
        // 3. Analyze current state
        let unlimitedPlans = 0;
        let fixedPlans = 0;
        let problemPlans = [];
        
        console.log('\n🔍 Analyzing current plans:');
        
        for (const plan of billionConnectPlans) {
            const metadata = plan.providerMetadata || {};
            const highFlowSize = metadata.highFlowSize;
            const capacity = metadata.capacity;
            
            console.log(`\n   📋 Plan: ${plan.name}`);
            console.log(`      SKU: ${plan.externalSkuId}`);
            console.log(`      Current planType: ${plan.planType}`);
            console.log(`      Current planData: ${plan.planData}`);
            console.log(`      Current planDataUnit: ${plan.planDataUnit}`);
            console.log(`      Metadata highFlowSize: ${highFlowSize}`);
            console.log(`      Metadata capacity: ${capacity}`);
            
            // Determine what the plan should be based on capacity logic
            let shouldBeUnlimited = false;
            let expectedPlanData = null;
            let expectedPlanDataUnit = null;
            
            if (capacity === -1) {
                shouldBeUnlimited = true;
                expectedPlanData = null;
                expectedPlanDataUnit = null;
            } else if (capacity && capacity > 0) {
                shouldBeUnlimited = false;
                // We need to get the correct planData and planDataUnit from the original product
                // Let's re-transform the product to get the correct values
                try {
                    const mockProduct = {
                        skuId: plan.externalSkuId,
                        highFlowSize: highFlowSize,
                        // Add minimal required fields for transformation
                        name: plan.name,
                        desc: plan.name,
                        days: "30", // Default
                        country: [{ name: "Unknown", mcc: "XX", operatorInfo: [{ operator: "Unknown", network: "4G" }] }]
                    };
                    
                    const transformedProduct = billionconnectService.transformProduct(mockProduct);
                    expectedPlanData = transformedProduct.planData;
                    expectedPlanDataUnit = transformedProduct.planDataUnit;
                } catch (error) {
                    console.log(`      ⚠️  Error re-transforming: ${error.message}`);
                    expectedPlanData = plan.planData; // Keep current
                    expectedPlanDataUnit = plan.planDataUnit; // Keep current
                }
            }
            
            const expectedPlanType = shouldBeUnlimited ? 'Unlimited' : 'Fixed';
            
            console.log(`      Expected planType: ${expectedPlanType}`);
            console.log(`      Expected planData: ${expectedPlanData}`);
            console.log(`      Expected planDataUnit: ${expectedPlanDataUnit}`);
            
            // Check if plan needs fixing
            const needsFixing = (
                plan.planType !== expectedPlanType ||
                plan.planData !== expectedPlanData ||
                plan.planDataUnit !== expectedPlanDataUnit
            );
            
            if (needsFixing) {
                console.log(`      ❌ NEEDS FIXING`);
                problemPlans.push({
                    id: plan.id,
                    name: plan.name,
                    externalSkuId: plan.externalSkuId,
                    current: {
                        planType: plan.planType,
                        planData: plan.planData,
                        planDataUnit: plan.planDataUnit
                    },
                    expected: {
                        planType: expectedPlanType,
                        planData: expectedPlanData,
                        planDataUnit: expectedPlanDataUnit
                    }
                });
            } else {
                console.log(`      ✅ CORRECT`);
            }
            
            if (shouldBeUnlimited) {
                unlimitedPlans++;
            } else {
                fixedPlans++;
            }
        }
        
        console.log(`\n📈 Summary:`);
        console.log(`   Total plans: ${billionConnectPlans.length}`);
        console.log(`   Should be unlimited: ${unlimitedPlans}`);
        console.log(`   Should be fixed: ${fixedPlans}`);
        console.log(`   Plans needing fixes: ${problemPlans.length}`);
        
        // 4. Fix the problematic plans
        if (problemPlans.length > 0) {
            console.log(`\n🔧 Fixing ${problemPlans.length} plans...`);
            
            for (const problemPlan of problemPlans) {
                try {
                    await EsimPlan.update(
                        {
                            planType: problemPlan.expected.planType,
                            planData: problemPlan.expected.planData,
                            planDataUnit: problemPlan.expected.planDataUnit
                        },
                        {
                            where: { id: problemPlan.id }
                        }
                    );
                    
                    console.log(`   ✅ Fixed: ${problemPlan.name}`);
                    console.log(`      ${problemPlan.current.planType} → ${problemPlan.expected.planType}`);
                    console.log(`      ${problemPlan.current.planData} ${problemPlan.current.planDataUnit} → ${problemPlan.expected.planData} ${problemPlan.expected.planDataUnit}`);
                    
                } catch (error) {
                    console.log(`   ❌ Error fixing ${problemPlan.name}: ${error.message}`);
                }
            }
            
            console.log(`\n🎉 Fixed ${problemPlans.length} BillionConnect plans!`);
        } else {
            console.log(`\n✅ All plans are already correct!`);
        }
        
        console.log(`\n🎯 Verification:`);
        console.log(`   - Plans with capacity = -1 should now have planType = "Unlimited" and planData = null`);
        console.log(`   - Plans with capacity > 0 should now have planType = "Fixed" and correct planData/planDataUnit`);
        console.log(`   - Frontend should now display "Unlimited" for unlimited plans`);
        
    } catch (error) {
        console.error('❌ Error fixing BillionConnect plans:', error);
    }
}

// Run the fix
fixBillionConnectPlans().catch(console.error);
