const { EsimPlan, Provider } = require('./src/models');
const billionconnectService = require('./src/services/billionconnect.service');
const providerFactory = require('./src/services/provider.factory');

async function testSyncUnlimited() {
    console.log('🔄 Testing Sync Process with Unlimited Plans\n');
    
    try {
        // 1. Find BillionConnect provider
        const provider = await Provider.findOne({
            where: { name: 'billionconnect' }
        });
        
        if (!provider) {
            console.log('❌ BillionConnect provider not found');
            return;
        }
        
        console.log(`✅ Found BillionConnect provider: ${provider.name}`);
        
        // 2. Simulate what happens during sync - create mock unlimited plans
        const mockUnlimitedPlans = [
            {
                skuId: "TEST_UNLIMITED_001",
                name: "Test Unlimited Plan 1",
                desc: "Test unlimited data plan 1",
                highFlowSize: "-1", // This should create unlimited plan
                days: "30",
                hotspotSupport: "0",
                limitFlowSpeed: "",
                country: [{
                    name: "Japan",
                    mcc: "JP",
                    operatorInfo: [{
                        operator: "SoftBank",
                        network: "5G"
                    }]
                }]
            },
            {
                skuId: "TEST_UNLIMITED_002", 
                name: "Test Unlimited Plan 2",
                desc: "Test unlimited data plan 2",
                highFlowSize: "-1", // This should create unlimited plan
                days: "7",
                hotspotSupport: "0",
                limitFlowSpeed: "",
                country: [{
                    name: "Thailand",
                    mcc: "TH",
                    operatorInfo: [{
                        operator: "AIS",
                        network: "4G"
                    }]
                }]
            },
            {
                skuId: "TEST_FIXED_001",
                name: "Test Fixed Plan 1GB",
                desc: "Test 1GB data plan",
                highFlowSize: "1048576", // 1GB in KB
                days: "7",
                hotspotSupport: "0",
                limitFlowSpeed: "",
                country: [{
                    name: "Korea",
                    mcc: "KR",
                    operatorInfo: [{
                        operator: "KT",
                        network: "5G"
                    }]
                }]
            }
        ];
        
        console.log(`\n🧪 Testing sync process with ${mockUnlimitedPlans.length} mock plans...\n`);
        
        for (const [index, mockPlan] of mockUnlimitedPlans.entries()) {
            console.log(`${index + 1}. Processing: ${mockPlan.name}`);
            console.log(`   Input highFlowSize: "${mockPlan.highFlowSize}"`);
            
            try {
                // Step 1: Transform through BillionConnect service (like sync does)
                const transformedProduct = billionconnectService.transformProduct(mockPlan);
                
                console.log(`   After BillionConnect transform:`);
                console.log(`     capacity: ${transformedProduct.capacity}`);
                console.log(`     planData: ${transformedProduct.planData}`);
                console.log(`     planDataUnit: ${transformedProduct.planDataUnit}`);
                console.log(`     isUnlimited: ${transformedProduct.isUnlimited}`);
                
                // Step 2: Standardize through Provider Factory (like sync does)
                const standardizedPlan = await providerFactory.standardizeProduct('billionconnect', transformedProduct);
                
                console.log(`   After Provider Factory standardize:`);
                console.log(`     planType: "${standardizedPlan.planType}"`);
                console.log(`     planData: ${standardizedPlan.planData}`);
                console.log(`     planDataUnit: ${standardizedPlan.planDataUnit}`);
                
                // Step 3: Simulate what sync would save to database
                const planDataForDB = {
                    externalSkuId: standardizedPlan.externalSkuId,
                    name: standardizedPlan.name,
                    planType: standardizedPlan.planType,
                    planData: standardizedPlan.planData,
                    planDataUnit: standardizedPlan.planDataUnit,
                    providerId: provider.id,
                    networkName: standardizedPlan.networkName,
                    networkType: standardizedPlan.networkType,
                    region: standardizedPlan.region,
                    buyingPrice: standardizedPlan.buyingPrice || 0,
                    validityDays: standardizedPlan.validityDays,
                    category: standardizedPlan.category,
                    planCategory: standardizedPlan.planCategory,
                    status: standardizedPlan.status,
                    isActive: true,
                    providerMetadata: {
                        ...standardizedPlan.providerMetadata,
                        capacity: transformedProduct.capacity,
                        highFlowSize: mockPlan.highFlowSize,
                        isUnlimited: transformedProduct.isUnlimited
                    }
                };
                
                console.log(`   Data that would be saved to DB:`);
                console.log(`     planType: "${planDataForDB.planType}"`);
                console.log(`     planData: ${planDataForDB.planData}`);
                console.log(`     planDataUnit: ${planDataForDB.planDataUnit}`);
                console.log(`     metadata.capacity: ${planDataForDB.providerMetadata.capacity}`);
                
                // Step 4: Test what frontend would display
                const frontendDisplay = planDataForDB.planType === "Unlimited" || planDataForDB.planData === -1 ? 
                    "Unlimited" : 
                    planDataForDB.planData && planDataForDB.planDataUnit ? 
                        `${planDataForDB.planData} ${planDataForDB.planDataUnit}` : 
                        "-";
                
                console.log(`   Frontend would display: "${frontendDisplay}"`);
                
                // Step 5: Verify correctness
                const expectedUnlimited = mockPlan.highFlowSize === "-1";
                const actualUnlimited = planDataForDB.planType === "Unlimited";
                const displayCorrect = expectedUnlimited ? frontendDisplay === "Unlimited" : frontendDisplay !== "Unlimited";
                
                console.log(`   Expected unlimited: ${expectedUnlimited}`);
                console.log(`   Actually unlimited: ${actualUnlimited}`);
                console.log(`   Display correct: ${displayCorrect ? '✅ YES' : '❌ NO'}`);
                
                if (!displayCorrect) {
                    console.log(`   ❌ PROBLEM: Expected "${expectedUnlimited ? 'Unlimited' : 'Fixed data amount'}" but got "${frontendDisplay}"`);
                }
                
            } catch (error) {
                console.log(`   ❌ Error processing plan: ${error.message}`);
            }
            
            console.log(''); // Empty line
        }
        
        // 6. Test with real BillionConnect API data if available
        console.log('🌐 Testing with real BillionConnect API data...');
        
        try {
            const realProducts = await billionconnectService.getProducts();
            console.log(`   Found ${realProducts.length} real products from BillionConnect API`);
            
            const unlimitedProducts = realProducts.filter(p => p.capacity === -1);
            const fixedProducts = realProducts.filter(p => p.capacity !== -1);
            
            console.log(`   Unlimited products: ${unlimitedProducts.length}`);
            console.log(`   Fixed products: ${fixedProducts.length}`);
            
            if (unlimitedProducts.length > 0) {
                console.log('\n   Sample unlimited product from API:');
                const sample = unlimitedProducts[0];
                console.log(`     Name: ${sample.name}`);
                console.log(`     SKU: ${sample.externalSkuId}`);
                console.log(`     Capacity: ${sample.capacity}`);
                console.log(`     IsUnlimited: ${sample.isUnlimited}`);
                console.log(`     PlanData: ${sample.planData}`);
                console.log(`     PlanDataUnit: ${sample.planDataUnit}`);
                
                // Test standardization of real unlimited product
                const standardized = await providerFactory.standardizeProduct('billionconnect', sample);
                console.log(`     After standardization:`);
                console.log(`       planType: "${standardized.planType}"`);
                console.log(`       planData: ${standardized.planData}`);
                console.log(`       planDataUnit: ${standardized.planDataUnit}`);
            }
            
        } catch (apiError) {
            console.log(`   ⚠️  Could not fetch real API data: ${apiError.message}`);
        }
        
    } catch (error) {
        console.error('❌ Error in test:', error);
    }
}

testSyncUnlimited().catch(console.error);
