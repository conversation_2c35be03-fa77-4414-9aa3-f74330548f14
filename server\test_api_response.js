const { EsimPlan, Provider } = require('./src/models');

async function testApiResponse() {
    console.log('🔍 Testing API Response for BillionConnect Plans\n');
    
    try {
        // Find BillionConnect provider
        const provider = await Provider.findOne({
            where: { name: 'billionconnect' }
        });
        
        if (!provider) {
            console.log('❌ BillionConnect provider not found');
            return;
        }
        
        // Get BillionConnect plans like the API would
        const plans = await EsimPlan.findAll({
            where: { 
                providerId: provider.id,
                isActive: true 
            },
            include: [{
                model: Provider,
                as: 'provider'
            }],
            limit: 5 // Just get a few for testing
        });
        
        console.log(`📊 Found ${plans.length} BillionConnect plans\n`);
        
        plans.forEach((plan, index) => {
            console.log(`${index + 1}. Plan: ${plan.name}`);
            console.log(`   SKU: ${plan.externalSkuId}`);
            console.log(`   planType: "${plan.planType}"`);
            console.log(`   planData: ${plan.planData}`);
            console.log(`   planDataUnit: ${plan.planDataUnit}`);
            console.log(`   Provider: ${plan.provider?.name}`);
            
            // Test the frontend logic
            const frontendDisplay = plan.planType === "Unlimited" || plan.planData === -1 ? 
                "Unlimited" : 
                plan.planData && plan.planDataUnit ? 
                    `${plan.planData} ${plan.planDataUnit}` : 
                    "-";
            
            console.log(`   Frontend Display: "${frontendDisplay}"`);
            
            // Check if this matches what we expect
            const isUnlimited = plan.planType === "Unlimited";
            const expectedDisplay = isUnlimited ? "Unlimited" : `${plan.planData} ${plan.planDataUnit}`;
            const isCorrect = frontendDisplay === expectedDisplay;
            
            console.log(`   Expected: "${expectedDisplay}"`);
            console.log(`   Status: ${isCorrect ? '✅ CORRECT' : '❌ INCORRECT'}`);
            
            // Show metadata for debugging
            if (plan.providerMetadata) {
                const metadata = plan.providerMetadata;
                console.log(`   Metadata:`);
                console.log(`     highFlowSize: ${metadata.highFlowSize}`);
                console.log(`     capacity: ${metadata.capacity}`);
                console.log(`     isUnlimited: ${metadata.isUnlimited}`);
            }
            
            console.log(''); // Empty line
        });
        
        // Test the specific unlimited plans
        const unlimitedPlans = plans.filter(plan => plan.planType === 'Unlimited');
        console.log(`🎯 Unlimited Plans Analysis:`);
        console.log(`   Found ${unlimitedPlans.length} unlimited plans`);
        
        unlimitedPlans.forEach((plan, index) => {
            console.log(`\n   ${index + 1}. ${plan.name}`);
            console.log(`      planType: "${plan.planType}"`);
            console.log(`      planData: ${plan.planData}`);
            console.log(`      planDataUnit: ${plan.planDataUnit}`);
            
            const metadata = plan.providerMetadata || {};
            console.log(`      capacity: ${metadata.capacity}`);
            console.log(`      highFlowSize: ${metadata.highFlowSize}`);
            
            // This is what the frontend should display
            const shouldDisplay = plan.planType === "Unlimited" || plan.planData === -1 ? 
                "Unlimited" : 
                `${plan.planData} ${plan.planDataUnit}`;
            
            console.log(`      Should display: "${shouldDisplay}"`);
            
            if (shouldDisplay !== "Unlimited") {
                console.log(`      ⚠️  WARNING: This unlimited plan will not display as "Unlimited"!`);
            }
        });
        
    } catch (error) {
        console.error('❌ Error:', error);
    }
}

testApiResponse().catch(console.error);
