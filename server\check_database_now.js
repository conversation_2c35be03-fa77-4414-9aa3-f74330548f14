const { EsimPlan, Provider } = require('./src/models');

async function checkDatabaseNow() {
    console.log('💾 CHECKING CURRENT DATABASE STATE\n');
    
    try {
        // Find BillionConnect provider
        const provider = await Provider.findOne({
            where: { name: 'billionconnect' }
        });
        
        if (!provider) {
            console.log('❌ BillionConnect provider not found');
            return;
        }
        
        console.log(`✅ Found BillionConnect provider: ${provider.name}`);
        
        // Get ALL BillionConnect plans from database
        const dbPlans = await EsimPlan.findAll({
            where: { providerId: provider.id },
            attributes: ['name', 'externalSkuId', 'planType', 'planData', 'planDataUnit', 'providerMetadata'],
            order: [['name', 'ASC']]
        });
        
        console.log(`\n📊 Found ${dbPlans.length} BillionConnect plans in database:\n`);
        
        let unlimitedCount = 0;
        let fixedCount = 0;
        let capacityMinusOneCount = 0;
        let capacityMinusOneButFixed = 0;
        
        dbPlans.forEach((plan, index) => {
            const metadata = plan.providerMetadata || {};
            
            console.log(`${index + 1}. ${plan.name}`);
            console.log(`   SKU: ${plan.externalSkuId}`);
            console.log(`   planType: "${plan.planType}"`);
            console.log(`   planData: ${plan.planData}`);
            console.log(`   planDataUnit: ${plan.planDataUnit}`);
            console.log(`   metadata.capacity: ${metadata.capacity}`);
            console.log(`   metadata.isUnlimited: ${metadata.isUnlimited}`);
            
            // Count by planType
            if (plan.planType === 'Unlimited') {
                unlimitedCount++;
                console.log(`   📋 TYPE: UNLIMITED`);
            } else if (plan.planType === 'Fixed') {
                fixedCount++;
                console.log(`   📋 TYPE: FIXED`);
            }
            
            // Count by capacity
            if (metadata.capacity === -1) {
                capacityMinusOneCount++;
                console.log(`   🔢 CAPACITY: -1 (should be unlimited)`);
                
                if (plan.planType !== 'Unlimited') {
                    capacityMinusOneButFixed++;
                    console.log(`   ❌ PROBLEM: capacity=-1 but planType="${plan.planType}"`);
                }
            } else {
                console.log(`   🔢 CAPACITY: ${metadata.capacity} (should be fixed)`);
            }
            
            console.log(''); // Empty line
        });
        
        console.log('📈 DATABASE SUMMARY:');
        console.log(`   Total plans: ${dbPlans.length}`);
        console.log(`   Plans with planType="Unlimited": ${unlimitedCount}`);
        console.log(`   Plans with planType="Fixed": ${fixedCount}`);
        console.log(`   Plans with capacity=-1: ${capacityMinusOneCount}`);
        console.log(`   Plans with capacity=-1 but planType="Fixed": ${capacityMinusOneButFixed}`);
        
        if (capacityMinusOneButFixed > 0) {
            console.log(`\n❌ CRITICAL ISSUE FOUND:`);
            console.log(`   ${capacityMinusOneButFixed} plans have capacity=-1 but are stored as "Fixed" in database`);
            console.log(`   These should be "Unlimited" plans but are incorrectly saved`);
            console.log(`   This means the sync process or provider factory has a bug`);
        } else {
            console.log(`\n✅ Database looks correct for capacity-based plan types`);
        }
        
        // Show specific plans that have the issue
        if (capacityMinusOneButFixed > 0) {
            console.log(`\n🔍 PROBLEMATIC PLANS (capacity=-1 but planType="Fixed"):`);
            
            const problematicPlans = dbPlans.filter(plan => {
                const metadata = plan.providerMetadata || {};
                return metadata.capacity === -1 && plan.planType !== 'Unlimited';
            });
            
            problematicPlans.forEach((plan, index) => {
                const metadata = plan.providerMetadata || {};
                console.log(`   ${index + 1}. ${plan.name}`);
                console.log(`      SKU: ${plan.externalSkuId}`);
                console.log(`      planType: "${plan.planType}" (should be "Unlimited")`);
                console.log(`      planData: ${plan.planData} (should be null)`);
                console.log(`      planDataUnit: ${plan.planDataUnit} (should be null)`);
                console.log(`      metadata.capacity: ${metadata.capacity}`);
            });
        }
        
    } catch (error) {
        console.error('❌ Error checking database:', error);
    }
}

checkDatabaseNow().catch(console.error);
