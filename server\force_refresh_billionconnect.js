const { EsimPlan, Provider } = require('./src/models');
const billionconnectService = require('./src/services/billionconnect.service');
const providerFactory = require('./src/services/provider.factory');

async function forceRefreshBillionConnect() {
    console.log('🔄 Force Refreshing BillionConnect Plans with Capacity-Based Logic\n');
    
    try {
        // 1. Find BillionConnect provider
        const provider = await Provider.findOne({
            where: { name: 'billionconnect' }
        });
        
        if (!provider) {
            console.log('❌ BillionConnect provider not found');
            return;
        }
        
        console.log(`✅ Found BillionConnect provider: ${provider.name}`);
        
        // 2. Get fresh data from BillionConnect API
        console.log('\n📡 Fetching fresh data from BillionConnect API...');
        const freshProducts = await billionconnectService.getProducts();
        console.log(`✅ Retrieved ${freshProducts.length} fresh products from API`);
        
        // 3. Find existing plans in database
        const existingPlans = await EsimPlan.findAll({
            where: { providerId: provider.id },
            attributes: ['id', 'externalSkuId', 'name', 'planType', 'planData', 'planDataUnit', 'providerMetadata']
        });
        
        console.log(`📊 Found ${existingPlans.length} existing plans in database`);
        
        // 4. Process each fresh product and update corresponding database plan
        let updatedCount = 0;
        let correctCount = 0;
        let errorCount = 0;
        
        console.log('\n🔧 Processing and updating plans...\n');
        
        for (const freshProduct of freshProducts) {
            try {
                // Find corresponding plan in database
                const existingPlan = existingPlans.find(plan => plan.externalSkuId === freshProduct.externalSkuId);
                
                if (!existingPlan) {
                    console.log(`⚠️  No existing plan found for SKU: ${freshProduct.externalSkuId}`);
                    continue;
                }
                
                console.log(`📋 Processing: ${existingPlan.name}`);
                console.log(`   SKU: ${freshProduct.externalSkuId}`);
                console.log(`   Fresh API capacity: ${freshProduct.capacity}`);
                console.log(`   Current DB planType: "${existingPlan.planType}"`);
                console.log(`   Current DB planData: ${existingPlan.planData}`);
                
                // Standardize the fresh product
                const standardizedPlan = await providerFactory.standardizeProduct('billionconnect', freshProduct);
                
                console.log(`   Standardized planType: "${standardizedPlan.planType}"`);
                console.log(`   Standardized planData: ${standardizedPlan.planData}`);
                console.log(`   Standardized planDataUnit: ${standardizedPlan.planDataUnit}`);
                
                // Check if update is needed
                const needsUpdate = (
                    existingPlan.planType !== standardizedPlan.planType ||
                    existingPlan.planData !== standardizedPlan.planData ||
                    existingPlan.planDataUnit !== standardizedPlan.planDataUnit
                );
                
                if (needsUpdate) {
                    console.log(`   🔧 UPDATING PLAN...`);
                    
                    // Update the plan with fresh standardized data
                    await existingPlan.update({
                        planType: standardizedPlan.planType,
                        planData: standardizedPlan.planData,
                        planDataUnit: standardizedPlan.planDataUnit,
                        providerMetadata: {
                            ...existingPlan.providerMetadata,
                            capacity: freshProduct.capacity,
                            isUnlimited: freshProduct.isUnlimited,
                            lastRefresh: new Date().toISOString()
                        }
                    });
                    
                    console.log(`   ✅ UPDATED: ${existingPlan.planType} → ${standardizedPlan.planType}`);
                    console.log(`   ✅ DATA: ${existingPlan.planData} ${existingPlan.planDataUnit} → ${standardizedPlan.planData} ${standardizedPlan.planDataUnit}`);
                    updatedCount++;
                } else {
                    console.log(`   ✅ ALREADY CORRECT`);
                    correctCount++;
                }
                
                // Test what frontend will display
                const frontendDisplay = standardizedPlan.planType === "Unlimited" || standardizedPlan.planData === -1 ? 
                    "Unlimited" : 
                    standardizedPlan.planData && standardizedPlan.planDataUnit ? 
                        `${standardizedPlan.planData} ${standardizedPlan.planDataUnit}` : 
                        "-";
                
                console.log(`   📱 Frontend will display: "${frontendDisplay}"`);
                
            } catch (error) {
                console.log(`   ❌ Error processing ${freshProduct.externalSkuId}: ${error.message}`);
                errorCount++;
            }
            
            console.log(''); // Empty line
        }
        
        // 5. Summary
        console.log('📈 REFRESH SUMMARY:');
        console.log(`   Total products processed: ${freshProducts.length}`);
        console.log(`   Plans updated: ${updatedCount}`);
        console.log(`   Plans already correct: ${correctCount}`);
        console.log(`   Errors: ${errorCount}`);
        
        // 6. Verify unlimited plans specifically
        console.log('\n🎯 UNLIMITED PLANS VERIFICATION:');
        const refreshedUnlimitedPlans = await EsimPlan.findAll({
            where: { 
                providerId: provider.id,
                planType: 'Unlimited'
            },
            attributes: ['name', 'externalSkuId', 'planType', 'planData', 'planDataUnit', 'providerMetadata']
        });
        
        console.log(`Found ${refreshedUnlimitedPlans.length} unlimited plans after refresh:`);
        
        refreshedUnlimitedPlans.forEach((plan, index) => {
            const metadata = plan.providerMetadata || {};
            console.log(`   ${index + 1}. ${plan.name}`);
            console.log(`      planType: "${plan.planType}"`);
            console.log(`      planData: ${plan.planData}`);
            console.log(`      planDataUnit: ${plan.planDataUnit}`);
            console.log(`      metadata.capacity: ${metadata.capacity}`);
            
            const frontendDisplay = plan.planType === "Unlimited" || plan.planData === -1 ? 
                "Unlimited" : 
                plan.planData && plan.planDataUnit ? 
                    `${plan.planData} ${plan.planDataUnit}` : 
                    "-";
            
            console.log(`      Frontend display: "${frontendDisplay}"`);
            
            if (frontendDisplay !== "Unlimited") {
                console.log(`      ⚠️  WARNING: This unlimited plan will not display as "Unlimited"!`);
            }
        });
        
        console.log('\n🎉 Force refresh completed!');
        console.log('💡 If you\'re still seeing issues in the frontend:');
        console.log('   1. Clear browser cache (Ctrl+F5)');
        console.log('   2. Check if you\'re looking at the correct environment');
        console.log('   3. Restart the frontend development server');
        
    } catch (error) {
        console.error('❌ Error in force refresh:', error);
    }
}

forceRefreshBillionConnect().catch(console.error);
