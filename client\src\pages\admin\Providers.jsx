import React, { useState, useEffect, useCallback } from 'react';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
    CardDescription,
} from '@/components/ui/card';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import api from '@/lib/axios';
import { Pencil, Plus, Trash, Eye, EyeOff } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

const formSchema = z.object({
    name: z.string().min(1, 'Name is required'),
    type: z.enum(['API', 'Custom']),
    country: z.string().optional(),
    apiEndpoint: z.string().optional().or(z.literal('')),
    apiKey: z.string().optional().or(z.literal('')),
    description: z.string().optional(),
    status: z.enum(['active', 'inactive']).default('active'),
});

export default function Providers() {
    const [providers, setProviders] = useState([]);
    const [isOpen, setIsOpen] = useState(false);
    const [editingProvider, setEditingProvider] = useState(null);
    const [loading, setLoading] = useState(true);
    const [hiddenProviders, setHiddenProviders] = useState(new Set());
    const [updatingVisibility, setUpdatingVisibility] = useState(new Set());
    const { toast } = useToast();
    const defaultValues = {
        name: '',
        type: 'Custom',
        country: '',
        apiEndpoint: '',
        apiKey: '',
        description: '',
        status: 'active'
    };

    const form = useForm({
        resolver: zodResolver(formSchema),
        defaultValues
    });

    const checkProviderPlanVisibility = useCallback(async (providersList) => {
        try {
            const hiddenProviderIds = new Set();

            // Check each provider's plan visibility
            for (const provider of providersList) {
                const response = await api.get(`/api/esim-plans?provider=${provider.id}&status=visible&limit=1`);
                // If no visible plans found, this provider's plans are hidden
                if (response.data.plans && response.data.plans.length === 0) {
                    hiddenProviderIds.add(provider.id);
                }
            }

            setHiddenProviders(hiddenProviderIds);
        } catch (error) {
            console.error('Error checking provider plan visibility:', error);
            // Don't show error toast for this as it's not critical
        }
    }, []);

    const fetchProviders = useCallback(async () => {
        try {
            const response = await api.get('/api/providers');
            setProviders(response.data);

            // Check which providers have all their plans hidden
            await checkProviderPlanVisibility(response.data);
        } catch (error) {
            toast({
                title: 'Error',
                description: 'Failed to fetch providers',
                variant: 'destructive',
            });
        } finally {
            setLoading(false);
        }
    }, [toast, checkProviderPlanVisibility]);

    useEffect(() => {
        fetchProviders();
    }, []);

    const onSubmit = async (data) => {
        try {
            if (editingProvider) {
                await api.put(`/api/providers/${editingProvider.id}`, data);
                toast({
                    title: 'Success',
                    description: 'Provider updated successfully',
                });
            } else {
                await api.post('/api/providers', data);
                toast({
                    title: 'Success',
                    description: 'Provider created successfully',
                });
            }
            setIsOpen(false);
            setEditingProvider(null);
            form.reset(defaultValues);
            fetchProviders();
        } catch (error) {
            toast({
                title: 'Error',
                description: error.response?.data?.message || 'Failed to save provider',
                variant: 'destructive',
            });
        }
    };

    const handleEdit = (provider) => {
        setEditingProvider(provider);
        form.reset(provider);
        setIsOpen(true);
    };

    const handleDelete = async (id) => {
        if (!window.confirm('Are you sure you want to delete this provider?')) {
            return;
        }

        try {
            await api.delete(`/api/providers/${id}`);
            toast({
                title: 'Success',
                description: 'Provider deleted successfully',
            });
            fetchProviders();
        } catch (error) {
            toast({
                title: 'Error',
                description: error.response?.data?.message || 'Failed to delete provider',
                variant: 'destructive',
            });
        }
    };

    const handleAddNew = () => {
        setEditingProvider(null);
        form.reset(defaultValues);
        setIsOpen(true);
    };

    const handleToggleProviderPlansVisibility = async (providerId, currentlyHidden) => {
        setUpdatingVisibility(prev => new Set(prev).add(providerId));

        try {
            // Update all plans for this provider
            const action = currentlyHidden ? 'show' : 'hide';
            const newStatus = currentlyHidden ? 'visible' : 'hidden';

            await api.put(`/api/esim-plans/provider/${providerId}/visibility`, {
                status: newStatus
            });

            // Update local state
            if (currentlyHidden) {
                setHiddenProviders(prev => {
                    const newSet = new Set(prev);
                    newSet.delete(providerId);
                    return newSet;
                });
            } else {
                setHiddenProviders(prev => new Set(prev).add(providerId));
            }

            toast({
                title: 'Success',
                description: `All plans for this provider have been ${action === 'hide' ? 'hidden' : 'shown'}`,
            });
        } catch (error) {
            toast({
                title: 'Error',
                description: error.response?.data?.message || 'Failed to update plan visibility',
                variant: 'destructive',
            });
        } finally {
            setUpdatingVisibility(prev => {
                const newSet = new Set(prev);
                newSet.delete(providerId);
                return newSet;
            });
        }
    };

    useEffect(() => {
        if (!isOpen) {
            setEditingProvider(null);
            form.reset(defaultValues);
        }
    }, [isOpen, form]);

    return (
        <div className="h-full flex flex-col gap-6 p-6">
            <Card className="p-4">
                <CardHeader className="bg-gradient-to-r from-blue-800 to-blue-600 mb-2 rounded-t-lg">
                    <div className="flex justify-between items-center">
                        <div>
                            <CardTitle className="text-white">Providers</CardTitle>
                            <CardDescription className="text-white/80">
                                Manage and track all providers
                            </CardDescription>
                        </div>
                        <Dialog open={isOpen} onOpenChange={setIsOpen}>
                            <DialogTrigger asChild>
                                <Button onClick={handleAddNew} className="bg-purple-500 text-white">
                                    <Plus className="w-4 h-4 mr-2" />
                                    Add Provider
                                </Button>
                            </DialogTrigger>
                            <DialogContent>
                                <DialogHeader>
                                    <DialogTitle>
                                        {editingProvider ? 'Edit Provider' : 'Add Provider'}
                                    </DialogTitle>
                                </DialogHeader>
                                <Form {...form}>
                                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                                        <FormField
                                            control={form.control}
                                            name="name"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Name</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="Enter provider name" />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        {/* Type Field */}
                                        <FormField
                                            control={form.control}
                                            name="type"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Type</FormLabel>
                                                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                                                        <FormControl>
                                                            <SelectTrigger>
                                                                <SelectValue placeholder="Select provider type" />
                                                            </SelectTrigger>
                                                        </FormControl>
                                                        <SelectContent>
                                                            <SelectItem value="API">API</SelectItem>
                                                            <SelectItem value="Custom">Custom</SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        {/* Country Field */}
                                        <FormField
                                            control={form.control}
                                            name="country"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Country</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="Enter country" />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        {/* Conditional Fields for API Type */}
                                        {form.watch('type') === 'API' && (
                                            <>
                                                {/* API Endpoint Field */}
                                                <FormField
                                                    control={form.control}
                                                    name="apiEndpoint"
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormLabel>API Endpoint</FormLabel>
                                                            <FormControl>
                                                                <Input {...field} placeholder="Enter API endpoint" />
                                                            </FormControl>
                                                            <FormMessage />
                                                        </FormItem>
                                                    )}
                                                />

                                                {/* API Key Field */}
                                                <FormField
                                                    control={form.control}
                                                    name="apiKey"
                                                    render={({ field }) => (
                                                        <FormItem>
                                                            <FormLabel>API Key</FormLabel>
                                                            <FormControl>
                                                                <Input type="password" {...field} placeholder="Enter API key" />
                                                            </FormControl>
                                                            <FormMessage />
                                                        </FormItem>
                                                    )}
                                                />
                                            </>
                                        )}

                                        {/* Description Field */}
                                        <FormField
                                            control={form.control}
                                            name="description"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Description</FormLabel>
                                                    <FormControl>
                                                        <Input {...field} placeholder="Enter description" />
                                                    </FormControl>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        {/* Status Field */}
                                        <FormField
                                            control={form.control}
                                            name="status"
                                            render={({ field }) => (
                                                <FormItem>
                                                    <FormLabel>Status</FormLabel>
                                                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                                                        <FormControl>
                                                            <SelectTrigger>
                                                                <SelectValue placeholder="Select status" />
                                                            </SelectTrigger>
                                                        </FormControl>
                                                        <SelectContent>
                                                            <SelectItem value="active">Active</SelectItem>
                                                            <SelectItem value="inactive">Inactive</SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                    <FormMessage />
                                                </FormItem>
                                            )}
                                        />

                                        {/* Submit Button */}
                                        <Button type="submit" className="w-full">
                                            {editingProvider ? 'Update Provider' : 'Add Provider'}
                                        </Button>
                                    </form>
                                </Form>
                            </DialogContent>
                        </Dialog>
                    </div>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow className="bg-gradient-to-r from-slate-100 to-gray-100">
                                <TableHead>Name</TableHead>
                                <TableHead>Type</TableHead>
                                <TableHead>Country</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead>Plans Visibility</TableHead>
                                <TableHead>Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {loading ? (
                                <TableRow>
                                    <TableCell colSpan={6} className="text-center py-8">
                                        <div className="flex items-center justify-center gap-2">
                                            <span className="animate-spin">⏳</span>
                                            Loading providers...
                                        </div>
                                    </TableCell>
                                </TableRow>
                            ) : providers.length === 0 ? (
                                <TableRow>
                                    <TableCell colSpan={6} className="text-center py-8">
                                        No providers found
                                    </TableCell>
                                </TableRow>
                            ) : (
                                providers.map((provider) => {
                                    const isHidden = hiddenProviders.has(provider.id);
                                    const isUpdating = updatingVisibility.has(provider.id);

                                    return (
                                        <TableRow key={provider.id} className="hover:bg-gray-50">
                                            <TableCell>{provider.name}</TableCell>
                                            <TableCell>{provider.type}</TableCell>
                                            <TableCell>{provider.country || '-'}</TableCell>
                                            <TableCell>
                                                <Badge
                                                    variant={provider.status === 'active' ? 'success' : 'destructive'}
                                                >
                                                    {provider.status}
                                                </Badge>
                                            </TableCell>
                                            <TableCell>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => handleToggleProviderPlansVisibility(provider.id, isHidden)}
                                                    disabled={isUpdating}
                                                    className="flex items-center gap-2"
                                                >
                                                    {isUpdating ? (
                                                        <span className="animate-spin">⏳</span>
                                                    ) : isHidden ? (
                                                        <Eye className="w-4 h-4" />
                                                    ) : (
                                                        <EyeOff className="w-4 h-4" />
                                                    )}
                                                    {isUpdating ? 'Updating...' : isHidden ? 'Show Plans' : 'Hide Plans'}
                                                </Button>
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex items-center gap-2">
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => handleEdit(provider)}
                                                    >
                                                        <Pencil className="w-4 h-4" />
                                                    </Button>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => handleDelete(provider.id)}
                                                        disabled={provider?.type === 'API'}
                                                        className={`${provider?.type === 'API' ? 'cursor-not-allowed opacity-50' : ''
                                                        }`}
                                                      title={provider?.type === 'API' ? 'Cannot delete Providers' : 'Delete Providers'}
                                                    >
                                                        <Trash className="w-4 h-4" />
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    );
                                })
                            )}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>
        </div>
    );
}