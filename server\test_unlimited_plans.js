const { EsimPlan, Provider } = require('./src/models');

async function testUnlimitedPlans() {
    console.log('🔍 Testing Unlimited BillionConnect Plans\n');
    
    try {
        // Find BillionConnect provider
        const provider = await Provider.findOne({
            where: { name: 'billionconnect' }
        });
        
        if (!provider) {
            console.log('❌ BillionConnect provider not found');
            return;
        }
        
        // Get ALL BillionConnect plans
        const allPlans = await EsimPlan.findAll({
            where: { 
                providerId: provider.id,
                isActive: true 
            },
            include: [{
                model: Provider,
                as: 'provider'
            }]
        });
        
        console.log(`📊 Found ${allPlans.length} total BillionConnect plans\n`);
        
        // Filter unlimited plans
        const unlimitedPlans = allPlans.filter(plan => plan.planType === 'Unlimited');
        console.log(`🎯 Found ${unlimitedPlans.length} unlimited plans:\n`);
        
        unlimitedPlans.forEach((plan, index) => {
            console.log(`${index + 1}. Plan: ${plan.name}`);
            console.log(`   SKU: ${plan.externalSkuId}`);
            console.log(`   planType: "${plan.planType}"`);
            console.log(`   planData: ${plan.planData}`);
            console.log(`   planDataUnit: ${plan.planDataUnit}`);
            
            // Test the frontend logic exactly as it appears in the admin page
            const frontendDisplay = plan.planType === "Unlimited" || plan.planData === -1 ? 
                "Unlimited" : 
                plan.planData && plan.planDataUnit ? 
                    `${plan.planData} ${plan.planDataUnit}` : 
                    "-";
            
            console.log(`   Frontend Display: "${frontendDisplay}"`);
            
            // Check metadata
            if (plan.providerMetadata) {
                const metadata = plan.providerMetadata;
                console.log(`   Metadata:`);
                console.log(`     highFlowSize: "${metadata.highFlowSize}"`);
                console.log(`     capacity: ${metadata.capacity}`);
                console.log(`     isUnlimited: ${metadata.isUnlimited}`);
            }
            
            // Verify this will display correctly
            const willDisplayUnlimited = (plan.planType === "Unlimited" || plan.planData === -1);
            console.log(`   Will display as Unlimited: ${willDisplayUnlimited ? '✅ YES' : '❌ NO'}`);
            
            if (!willDisplayUnlimited) {
                console.log(`   ⚠️  WARNING: This plan has planType="Unlimited" but will not display as "Unlimited"!`);
                console.log(`   ⚠️  Reason: planType="${plan.planType}", planData=${plan.planData}`);
            }
            
            console.log(''); // Empty line
        });
        
        // Also check for any plans that might have planData = -1 but planType != "Unlimited"
        const planDataMinusOne = allPlans.filter(plan => plan.planData === -1);
        console.log(`🔍 Plans with planData = -1: ${planDataMinusOne.length}`);
        
        planDataMinusOne.forEach((plan, index) => {
            console.log(`   ${index + 1}. ${plan.name} - planType: "${plan.planType}", planData: ${plan.planData}`);
        });
        
        // Show a few fixed plans for comparison
        const fixedPlans = allPlans.filter(plan => plan.planType === 'Fixed').slice(0, 3);
        console.log(`\n📋 Sample Fixed Plans (for comparison):`);
        
        fixedPlans.forEach((plan, index) => {
            console.log(`   ${index + 1}. ${plan.name}`);
            console.log(`      planType: "${plan.planType}", planData: ${plan.planData}, planDataUnit: ${plan.planDataUnit}`);
            
            const frontendDisplay = plan.planType === "Unlimited" || plan.planData === -1 ? 
                "Unlimited" : 
                plan.planData && plan.planDataUnit ? 
                    `${plan.planData} ${plan.planDataUnit}` : 
                    "-";
            console.log(`      Frontend Display: "${frontendDisplay}"`);
        });
        
    } catch (error) {
        console.error('❌ Error:', error);
    }
}

testUnlimitedPlans().catch(console.error);
