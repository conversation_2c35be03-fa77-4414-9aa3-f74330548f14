const billionconnectService = require('./src/services/billionconnect.service');
const providerFactory = require('./src/services/provider.factory');

async function testCapacityLogic() {
    console.log('🧪 Testing Capacity-Based Data Display Logic\n');
    
    // Test cases with different capacity scenarios
    const testCases = [
        {
            name: 'Unlimited Plan (capacity = -1)',
            mockProduct: {
                skuId: "TEST_UNLIMITED",
                name: "Test Unlimited Plan",
                desc: "Test unlimited data plan",
                highFlowSize: "-1", // This will set capacity to -1
                days: "30",
                hotspotSupport: "0",
                limitFlowSpeed: "",
                country: [{
                    name: "Test Country",
                    mcc: "TC",
                    operatorInfo: [{
                        operator: "Test Operator",
                        network: "5G"
                    }]
                }]
            }
        },
        {
            name: 'Fixed Plan 1GB (capacity = 1)',
            mockProduct: {
                skuId: "TEST_1GB",
                name: "Test 1GB Plan",
                desc: "Test 1GB data plan",
                highFlowSize: "1048576", // 1GB in KB
                days: "7",
                hotspotSupport: "0",
                limitFlowSpeed: "",
                country: [{
                    name: "Test Country",
                    mcc: "TC",
                    operatorInfo: [{
                        operator: "Test Operator",
                        network: "4G"
                    }]
                }]
            }
        },
        {
            name: 'Fixed Plan 500MB (capacity = 500)',
            mockProduct: {
                skuId: "TEST_500MB",
                name: "Test 500MB Plan",
                desc: "Test 500MB data plan",
                highFlowSize: "512000", // 500MB in KB
                days: "3",
                hotspotSupport: "0",
                limitFlowSpeed: "",
                country: [{
                    name: "Test Country",
                    mcc: "TC",
                    operatorInfo: [{
                        operator: "Test Operator",
                        network: "4G"
                    }]
                }]
            }
        }
    ];
    
    for (const testCase of testCases) {
        console.log(`📋 Testing: ${testCase.name}`);
        console.log(`   Input highFlowSize: "${testCase.mockProduct.highFlowSize}"`);
        
        try {
            // Step 1: Transform through BillionConnect service
            const transformedProduct = billionconnectService.transformProduct(testCase.mockProduct);
            
            console.log(`   BillionConnect Service Output:`);
            console.log(`     - capacity: ${transformedProduct.capacity}`);
            console.log(`     - planData: ${transformedProduct.planData}`);
            console.log(`     - planDataUnit: ${transformedProduct.planDataUnit}`);
            console.log(`     - isUnlimited: ${transformedProduct.isUnlimited}`);
            
            // Step 2: Standardize through Provider Factory
            const standardizedProduct = await providerFactory.standardizeProduct('billionconnect', transformedProduct);
            
            console.log(`   Provider Factory Output:`);
            console.log(`     - planType: ${standardizedProduct.planType}`);
            console.log(`     - planData: ${standardizedProduct.planData}`);
            console.log(`     - planDataUnit: ${standardizedProduct.planDataUnit}`);
            
            // Step 3: Verify the logic
            const expectedPlanType = transformedProduct.capacity === -1 ? 'Unlimited' : 'Fixed';
            const expectedPlanData = transformedProduct.capacity === -1 ? null : transformedProduct.planData;
            const expectedPlanDataUnit = transformedProduct.capacity === -1 ? null : transformedProduct.planDataUnit;
            
            const isCorrect = (
                standardizedProduct.planType === expectedPlanType &&
                standardizedProduct.planData === expectedPlanData &&
                standardizedProduct.planDataUnit === expectedPlanDataUnit
            );
            
            console.log(`   ✅ Verification: ${isCorrect ? 'PASSED' : 'FAILED'}`);
            
            if (!isCorrect) {
                console.log(`   ❌ Expected: planType=${expectedPlanType}, planData=${expectedPlanData}, planDataUnit=${expectedPlanDataUnit}`);
                console.log(`   ❌ Actual: planType=${standardizedProduct.planType}, planData=${standardizedProduct.planData}, planDataUnit=${standardizedProduct.planDataUnit}`);
            }
            
            // Step 4: Check data display in planInfo
            const planInfoContent = standardizedProduct.planInfo;
            let dataDisplayInHTML = 'Not found';
            
            if (planInfoContent) {
                const dataMatch = planInfoContent.match(/Data: ([^<]+)/);
                if (dataMatch) {
                    dataDisplayInHTML = dataMatch[1].trim();
                }
            }
            
            console.log(`   📄 Data Display in HTML: "${dataDisplayInHTML}"`);
            
            const expectedDataDisplay = transformedProduct.capacity === -1 ? 'Unlimited' : 
                `${transformedProduct.planData} ${transformedProduct.planDataUnit}`;
            
            const displayCorrect = dataDisplayInHTML === expectedDataDisplay;
            console.log(`   ✅ Display Verification: ${displayCorrect ? 'PASSED' : 'FAILED'}`);
            
            if (!displayCorrect) {
                console.log(`   ❌ Expected Display: "${expectedDataDisplay}"`);
                console.log(`   ❌ Actual Display: "${dataDisplayInHTML}"`);
            }
            
        } catch (error) {
            console.log(`   ❌ Error: ${error.message}`);
        }
        
        console.log(''); // Empty line for readability
    }
    
    console.log('🎯 Summary:');
    console.log('   - Capacity field is now correctly used for plan type determination');
    console.log('   - When capacity = -1: planType = "Unlimited", planData = null, planDataUnit = null');
    console.log('   - When capacity > 0: planType = "Fixed", planData = actual amount, planDataUnit = MB/GB');
    console.log('   - Data display in HTML shows "Unlimited" for unlimited plans and "X GB/MB" for fixed plans');
}

// Run the test
testCapacityLogic().catch(console.error);
