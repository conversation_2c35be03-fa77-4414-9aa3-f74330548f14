const { EsimPlan, Provider } = require('./src/models');
const billionconnectService = require('./src/services/billionconnect.service');
const providerFactory = require('./src/services/provider.factory');

async function debugUnlimitedIssue() {
    console.log('🐛 Debugging Unlimited Plans Issue\n');
    
    try {
        // 1. Get raw data from BillionConnect API
        console.log('1. Fetching raw data from BillionConnect API...');
        const rawResponse = await billionconnectService.getCommodities();
        console.log(`   Found ${rawResponse.length} raw commodities`);
        
        // 2. Find unlimited plans in raw data
        const unlimitedRaw = rawResponse.filter(item => item.highFlowSize === "-1" || item.highFlowSize === -1);
        console.log(`   Found ${unlimitedRaw.length} unlimited plans in raw data:`);
        
        unlimitedRaw.forEach((item, index) => {
            console.log(`     ${index + 1}. ${item.name}`);
            console.log(`        SKU: ${item.skuId}`);
            console.log(`        highFlowSize: "${item.highFlowSize}" (type: ${typeof item.highFlowSize})`);
        });
        
        // 3. Transform through BillionConnect service
        console.log('\n2. Testing BillionConnect service transformation...');
        
        if (unlimitedRaw.length > 0) {
            const testUnlimited = unlimitedRaw[0];
            console.log(`   Testing with: ${testUnlimited.name}`);
            console.log(`   Raw highFlowSize: "${testUnlimited.highFlowSize}" (type: ${typeof testUnlimited.highFlowSize})`);
            
            const transformed = billionconnectService.transformProduct(testUnlimited);
            console.log(`   After transformation:`);
            console.log(`     capacity: ${transformed.capacity} (type: ${typeof transformed.capacity})`);
            console.log(`     isUnlimited: ${transformed.isUnlimited}`);
            console.log(`     planData: ${transformed.planData}`);
            console.log(`     planDataUnit: ${transformed.planDataUnit}`);
            
            // 4. Test provider factory standardization
            console.log('\n3. Testing Provider Factory standardization...');
            const standardized = await providerFactory.standardizeProduct('billionconnect', transformed);
            console.log(`   After standardization:`);
            console.log(`     planType: "${standardized.planType}"`);
            console.log(`     planData: ${standardized.planData}`);
            console.log(`     planDataUnit: ${standardized.planDataUnit}`);
            
            // 5. Check the logic step by step
            console.log('\n4. Step-by-step logic check:');
            console.log(`   transformed.capacity === -1: ${transformed.capacity === -1}`);
            console.log(`   transformed.capacity: ${transformed.capacity}`);
            console.log(`   typeof transformed.capacity: ${typeof transformed.capacity}`);
            
            // Test the exact condition from provider factory
            const planTypeCondition = transformed.capacity === -1;
            const expectedPlanType = planTypeCondition ? 'Unlimited' : 'Fixed';
            console.log(`   Condition result: ${planTypeCondition}`);
            console.log(`   Expected planType: "${expectedPlanType}"`);
            console.log(`   Actual planType: "${standardized.planType}"`);
            
            if (expectedPlanType !== standardized.planType) {
                console.log(`   ❌ MISMATCH FOUND!`);
                console.log(`   The condition 'transformed.capacity === -1' is: ${planTypeCondition}`);
                console.log(`   But planType is: "${standardized.planType}"`);
            } else {
                console.log(`   ✅ Logic is correct`);
            }
        }
        
        // 6. Check what's actually in the database
        console.log('\n5. Checking database records...');
        const provider = await Provider.findOne({ where: { name: 'billionconnect' } });
        
        if (provider) {
            const dbPlans = await EsimPlan.findAll({
                where: { providerId: provider.id },
                attributes: ['name', 'externalSkuId', 'planType', 'planData', 'planDataUnit', 'providerMetadata']
            });
            
            console.log(`   Found ${dbPlans.length} plans in database`);
            
            const dbUnlimited = dbPlans.filter(plan => {
                const metadata = plan.providerMetadata || {};
                return metadata.capacity === -1 || plan.planType === 'Unlimited';
            });
            
            console.log(`   Plans that should be unlimited (capacity = -1): ${dbUnlimited.length}`);
            
            dbUnlimited.forEach((plan, index) => {
                const metadata = plan.providerMetadata || {};
                console.log(`     ${index + 1}. ${plan.name}`);
                console.log(`        SKU: ${plan.externalSkuId}`);
                console.log(`        planType: "${plan.planType}"`);
                console.log(`        planData: ${plan.planData}`);
                console.log(`        planDataUnit: ${plan.planDataUnit}`);
                console.log(`        metadata.capacity: ${metadata.capacity}`);
                console.log(`        metadata.isUnlimited: ${metadata.isUnlimited}`);
                
                // Check if this plan is incorrectly set
                if (metadata.capacity === -1 && plan.planType !== 'Unlimited') {
                    console.log(`        ❌ INCORRECT: Should be Unlimited but is ${plan.planType}`);
                } else if (metadata.capacity === -1 && plan.planType === 'Unlimited') {
                    console.log(`        ✅ CORRECT: Unlimited plan`);
                }
            });
        }
        
        // 7. Test the exact sync process
        console.log('\n6. Testing exact sync process...');
        const products = await billionconnectService.getProducts();
        const unlimitedProducts = products.filter(p => p.capacity === -1);
        
        console.log(`   getProducts() returned ${products.length} products`);
        console.log(`   ${unlimitedProducts.length} have capacity = -1`);
        
        if (unlimitedProducts.length > 0) {
            const testProduct = unlimitedProducts[0];
            console.log(`   Testing sync process with: ${testProduct.name}`);
            console.log(`   Product capacity: ${testProduct.capacity}`);
            console.log(`   Product isUnlimited: ${testProduct.isUnlimited}`);
            
            const syncStandardized = await providerFactory.standardizeProduct('billionconnect', testProduct);
            console.log(`   Sync would create:`);
            console.log(`     planType: "${syncStandardized.planType}"`);
            console.log(`     planData: ${syncStandardized.planData}`);
            console.log(`     planDataUnit: ${syncStandardized.planDataUnit}`);
        }
        
    } catch (error) {
        console.error('❌ Error in debug:', error);
        console.error('Stack trace:', error.stack);
    }
}

debugUnlimitedIssue().catch(console.error);
